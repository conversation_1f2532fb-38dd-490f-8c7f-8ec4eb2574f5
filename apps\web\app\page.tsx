import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Badge } from '@workspace/ui/components/badge';
import { ThemeToggle } from '@workspace/ui/components/theme-toggle';
import {
  Target,
  MessageSquare,
  Trophy,
  TrendingUp,
  Users,
  Briefcase,
  ArrowRight
} from 'lucide-react';

const features = [
  {
    icon: Target,
    title: 'Milestone-Based Learning',
    description: 'Progress through structured milestones designed to build your career skills step by step.',
    color: 'bg-blue-50 text-blue-600 dark:bg-blue-950/50 dark:text-blue-400',
  },
  {
    icon: MessageSquare,
    title: 'AI Career Coaches',
    description: 'Get personalized guidance from specialized AI agents including coaches, mentors, and buddies.',
    color: 'bg-purple-50 text-purple-600 dark:bg-purple-950/50 dark:text-purple-400',
  },
  {
    icon: Briefcase,
    title: 'Career Explorer',
    description: 'Discover and compare career roles that match your skills, interests, and goals.',
    color: 'bg-green-50 text-green-600 dark:bg-green-950/50 dark:text-green-400',
  },
  {
    icon: Trophy,
    title: 'Gamified Experience',
    description: 'Earn X<PERSON>, unlock achievements, and track your progress with engaging gamification.',
    color: 'bg-yellow-50 text-yellow-600 dark:bg-yellow-950/50 dark:text-yellow-400',
  },
  {
    icon: TrendingUp,
    title: 'Personalized Roadmaps',
    description: 'Get custom learning paths tailored to your target career and current skill level.',
    color: 'bg-orange-50 text-orange-600 dark:bg-orange-950/50 dark:text-orange-400',
  },
  {
    icon: Users,
    title: 'Peer Learning',
    description: 'Connect with other learners, share experiences, and get feedback on your work.',
    color: 'bg-pink-50 text-pink-600 dark:bg-pink-950/50 dark:text-pink-400',
  },
];

export default function Page() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-background to-purple-50 dark:from-blue-950/20 dark:via-background dark:to-purple-950/20">
      {/* Header */}
      <header className="border-b bg-background/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">K</span>
              </div>
              <span className="font-bold text-xl text-foreground">Kaizen Project</span>
            </div>
            <div className="flex items-center gap-4">
              <ThemeToggle />
              <Button>Get Started</Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto text-center">
          <div>
            <Badge variant="outline" className="mb-6">
              🚀 Transform Your Career Journey
            </Badge>
            <h1 className="text-5xl sm:text-6xl font-bold text-foreground mb-6">
              Your AI-Powered
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {" "}Career Coach
              </span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              Navigate your career journey with personalized milestones, AI guidance,
              and a gamified learning experience designed to help you achieve your professional goals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="text-lg px-8 py-3">
                Start Your Journey
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8 py-3">
                Watch Demo
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">
              Everything You Need to Succeed
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Our comprehensive platform combines AI technology with proven career development
              methodologies to accelerate your professional growth.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature) => {
              const Icon = feature.icon;
              return (
                <Card key={feature.title} className="h-full hover:shadow-lg transition-shadow duration-200">
                  <CardHeader>
                    <div className={`w-12 h-12 rounded-lg ${feature.color} flex items-center justify-center mb-4`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-base">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-800 dark:to-purple-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div>
            <h2 className="text-4xl font-bold text-white mb-4">
              Ready to Transform Your Career?
            </h2>
            <p className="text-xl text-blue-100 dark:text-blue-200 mb-8 max-w-2xl mx-auto">
              Join thousands of professionals who are already using Kaizen to accelerate
              their career growth and achieve their goals.
            </p>
            <Button size="lg" variant="secondary" className="text-lg px-8 py-3">
              Get Started Now
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
